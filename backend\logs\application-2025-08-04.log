{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'error',
  message: 'Database connection failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async testConnection (D:\\Task_Project\\backend\\test-db.js:9:5)',
  timestamp: '2025-08-04 06:21:14'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:21:14'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'error',
  message: 'Database connection failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async testConnection (D:\\Task_Project\\backend\\test-db.js:23:5)',
  timestamp: '2025-08-04 06:22:58'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:22:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:24:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:24:23'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:24:23'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Starting database migrations...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT MAX(version) as version FROM migrations',
  duration: '11ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Current migration version: 0',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Found 3 pending migrations',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Running migration 1: create_initial_tables',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '2ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Migration 1 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Running migration 2: add_indexes',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '1ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Migration 2 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Running migration 3: add_migration_tracking',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Migration 3 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'All migrations completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:19'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:25:19'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:25:19'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Categories seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Courses seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Lessons seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:26:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:14'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:44'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:54'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  path: './config/firebase-admin-sdk.json',
  error: "Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  level: 'warn',
  message: 'Firebase Admin SDK file not found. Firebase features will be disabled.',
  timestamp: '2025-08-04 06:29:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  path: './config/firebase-admin-sdk.json',
  error: "Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  level: 'warn',
  message: 'Firebase Admin SDK file not found. Firebase features will be disabled.',
  timestamp: '2025-08-04 06:31:19'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:32:15'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:32:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:32:16'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:32:16'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:32:16'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:32:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '1d31b984-bf02-47d1-98bd-8df672c5955a',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '3ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'dc368e4f-19a0-4a83-9c9b-6ffa613127bc',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '1ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a8af1690-0d26-43f0-962f-f8caed7f435f',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:23'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '38ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '44ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a8af1690-0d26-43f0-962f-f8caed7f435f',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '44ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '1fd606a3-56aa-4caa-81e6-69b28815a078',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:24'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '143ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3218eb4c-c9af-4a5c-9448-e967a555ba50',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '1ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3218eb4c-c9af-4a5c-9448-e967a555ba50',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '4ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '20e102a2-7aba-4339-8715-695ffc7c5fd5',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:34:24'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '35ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '37ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '20e102a2-7aba-4339-8715-695ffc7c5fd5',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '37ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:48'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:48'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:35:48'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:48'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:48'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:48'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '49461c38-9e95-4efe-86b3-cc71fc0db61d',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '3ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '93ad4f1e-a56a-4b01-99d1-131d9a400a62',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '1ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'adf050f3-2370-4362-877f-4ab409be7a38',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '6ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '10ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'adf050f3-2370-4362-877f-4ab409be7a38',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '10ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '638aa800-a438-4188-af9f-50b3c22533d7',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '139ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '902c021d-d644-4920-8c24-cf4440e580a8',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '1ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '902c021d-d644-4920-8c24-cf4440e580a8',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '4ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4de9af76-5a11-4473-8398-bbf7ecd1d760',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:35:56'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '144ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '148ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4de9af76-5a11-4473-8398-bbf7ecd1d760',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '148ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:57'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:37:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:37:46'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:37:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e106aab4-b61f-4566-b1c8-1a1ad2b81e68',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: 'curl/8.8.0',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:38:00'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '133ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.8.0',
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '137ms',
  userAgent: 'curl/8.8.0',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e106aab4-b61f-4566-b1c8-1a1ad2b81e68',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '137ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:38:00'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:19'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:19'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:40:19'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:19'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:19'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:19'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e204e527-e770-46d2-a1c1-aefa67b584fd',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '2ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '2e1c0a6a-666d-43ca-8412-6cbca9bfee32',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '1ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '5f77ca70-278c-4724-8874-72357f06808a',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '05509197-e91e-4cf8-92c8-e92200789209',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '6ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a7122903-72b5-4b9e-959d-690e85448e6d',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '2ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '9ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a7122903-72b5-4b9e-959d-690e85448e6d',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '9ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '75ed0011-7d27-4fda-b41d-064f824595fe',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:28'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '144ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '148ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '75ed0011-7d27-4fda-b41d-064f824595fe',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '148ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '73de982a-67c5-4fc3-a7dc-3ea1c4e39fa7',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::1',
  userAgent: 'curl/8.8.0',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:40:47'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '33ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.8.0',
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '35ms',
  userAgent: 'curl/8.8.0',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '73de982a-67c5-4fc3-a7dc-3ea1c4e39fa7',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '35ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:47'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:43:20'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:43:20'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:43:20'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:43:20'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:43:20'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:43:20'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f1f1825b-fbad-4011-a625-b3f8a681d439',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '56973ece-9ba7-485c-a3fb-cfe96c0273be',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '1ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4bcc7f10-d6be-40b5-98bd-ec0238a8d0e9',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '6ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '5fff54e2-c4e8-4eb7-af6f-604c40f9b04d',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '7ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'b75078ab-f882-428d-b5dd-0d67bf0a5c8d',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 200,
  responseTime: '2ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '05f06ddf-7b56-4ed4-b36e-b54ee4c3e28b',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: undefined,
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:43:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 200,
  responseTime: '2ms',
  userAgent: undefined,
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:43:30'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:18'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 06:45:58'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:58'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:58'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:45:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '48fe814c-3df4-461e-bfd7-505581ef9a63',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: 'PostmanRuntime/7.44.1',
  contentType: 'application/json',
  contentLength: '71',
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:46:53'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '3ms',
  userAgent: 'PostmanRuntime/7.44.1',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:46:53'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f6da85ea-5ade-4130-b2e1-8136f934a9ce',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: 'PostmanRuntime/7.44.1',
  contentType: 'application/json',
  contentLength: '71',
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:47:08'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 06:47:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '42ms',
  userAgent: 'PostmanRuntime/7.44.1',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:47:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6f4de85f-33b3-4899-8606-22de2be5c839',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: 'PostmanRuntime/7.44.1',
  contentType: 'application/json',
  contentLength: '71',
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:47:22'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 200,
  responseTime: '4ms',
  userAgent: 'PostmanRuntime/7.44.1',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:47:22'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6a609efe-d1b6-4077-9461-a3dab0b286fe',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: 'PostmanRuntime/7.44.1',
  contentType: 'application/json',
  contentLength: '71',
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:47:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '4ms',
  userAgent: 'PostmanRuntime/7.44.1',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:47:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f9804d06-01b7-4d1d-8526-ec6b24020cbd',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::1',
  userAgent: 'PostmanRuntime/7.44.1',
  contentType: 'application/json',
  contentLength: '71',
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 06:47:40'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '3ms',
  userAgent: 'PostmanRuntime/7.44.1',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:47:40'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:06:34'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:06:34'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 08:06:34'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:06:34'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:06:34'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:06:34'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '0851f52e-7834-4159-a3e2-a20b7300eb91',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '78ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '766eeb4b-45b1-4ab6-8d07-1a53f1555341',
  method: 'GET',
  url: '/favicon.ico',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  responseTime: '8ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '766eeb4b-45b1-4ab6-8d07-1a53f1555341',
  statusCode: 404,
  method: 'GET',
  url: '/favicon.ico',
  responseTime: '8ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3eb3811a-cea5-4797-b1c9-c9db3d657d54',
  method: 'GET',
  url: '/courses',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 08:49:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/courses',
  statusCode: 404,
  responseTime: '3ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3eb3811a-cea5-4797-b1c9-c9db3d657d54',
  statusCode: 404,
  method: 'GET',
  url: '/courses',
  responseTime: '3ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 08:49:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6c92d26a-61cb-497a-b2d3-94c847fbc349',
  method: 'GET',
  url: '/api/courses',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 08:49:51'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:49:51'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses',
  statusCode: 200,
  responseTime: '219ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:51'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'c068617f-40e4-4891-bb76-cb7d0433abb6',
  method: 'GET',
  url: '/api/courses/popular',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 08:50:46'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 08:50:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 200,
  responseTime: '47ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:50:46'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:24'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:19:24'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:24'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:24'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:24'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:38'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:38'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:19:38'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:38'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:38'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:19:38'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:27'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:20:27'
}
{
  message: 'Starting database migrations...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:27'
}
{
  message: 'Current migration version: 3',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:27'
}
{
  message: 'No pending migrations',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:27'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:27'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Categories seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Courses seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Lessons seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:39'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:20:54'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:03'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:21:03'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:03'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:03'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:03'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:39'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:39'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:21:39'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:39'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:39'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:21:39'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:22:56'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:22:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:22:56'
}
{
  message: 'Server is running on port 3002',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:22:56'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:22:56'
}
{
  message: 'Health check available at: http://localhost:3002/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:22:56'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:24:10'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:24:10'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:24:10'
}
{
  message: 'Server is running on port 3003',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:24:10'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:24:10'
}
{
  message: 'Health check available at: http://localhost:3003/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:24:10'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f770ef8e-745b-4f3f-8549-ce8271d5e158',
  method: 'GET',
  url: '/health',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 09:24:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  responseTime: '4ms',
  userAgent: 'Augment-VSCode/1.0',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 09:24:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4e16f8b0-13c5-4c35-86d3-f02b1d15e40a',
  method: 'GET',
  url: '/api/courses/featured',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  contentType: undefined,
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-04 09:24:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 200,
  responseTime: '8ms',
  userAgent: 'Augment-VSCode/1.0',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-04 09:24:35'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:18'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:38'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:39'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 09:26:39'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:39'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:39'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 09:26:39'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:49:12'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:49:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: '************************************************************************************************************',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL ************************************************************************************************************',
  stack: 'Error: getaddrinfo EAI_FAIL **************************************************************************************************************' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:49:16'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:50:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: 'postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  stack: 'Error: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:04'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:50:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: 'postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  stack: 'Error: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:08'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:50:41'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: '************************************************************************************************************',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL ************************************************************************************************************',
  stack: 'Error: getaddrinfo EAI_FAIL **************************************************************************************************************' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:41'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:51:55'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:51:56'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:52:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:10'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:52:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:46'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:52:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:58'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:58:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:58:58'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:59:26'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:59:28'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 10:59:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -4077,
  code: 'ECONNRESET',
  syscall: 'read',
  level: 'error',
  message: 'Database connection failed: read ECONNRESET',
  stack: 'Error: read ECONNRESET\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:59:56'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:01:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  length: 107,
  name: 'error',
  severity: 'FATAL',
  code: '3D000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postinit.c',
  line: '1032',
  routine: 'InitPostgres',
  level: 'error',
  message: 'Database connection failed: database "quipgen_elearning_app" does not exist',
  stack: 'error: database "quipgen_elearning_app" does not exist\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:01:06'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:01:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  length: 107,
  name: 'error',
  severity: 'FATAL',
  code: '3D000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postinit.c',
  line: '1032',
  routine: 'InitPostgres',
  level: 'error',
  message: 'Database connection failed: database "quipgen_elearning_app" does not exist',
  stack: 'error: database "quipgen_elearning_app" does not exist\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:01:47'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:02:09'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:02:10'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:02:11'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:02:11'
}
{
  message: 'Environment: production',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:02:11'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:02:11'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:33:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'error',
  message: 'Database connection failed: Connection terminated due to connection timeout',
  stack: 'Error: Connection terminated due to connection timeout\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  cause: Error: Connection terminated unexpectedly
      at Connection.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\client.js:136:73)
      at Object.onceWrapper (node:events:632:28)
      at Connection.emit (node:events:518:28)
      at Socket.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\connection.js:62:12)
      at Socket.emit (node:events:530:35)
      at TCP.<anonymous> (node:net:343:12),
  timestamp: '2025-08-04 11:34:00'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:18'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:34:19'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:37'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:39'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:34:39'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:39'
}
{
  message: 'Environment: production',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:39'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:34:39'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:35:02'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:35:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:35:04'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:35:04'
}
{
  message: 'Environment: production',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:35:04'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:35:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'error',
  message: 'Database connection failed: Connection terminated due to connection timeout',
  stack: 'Error: Connection terminated due to connection timeout\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async runMigrations (D:\\Task_Project\\backend\\src\\database\\migrate.js:206:5)\n' +
    '    at async main (D:\\Task_Project\\backend\\src\\database\\migrate.js:302:9)',
  cause: Error: Connection terminated unexpectedly
      at Connection.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\client.js:136:73)
      at Object.onceWrapper (node:events:632:28)
      at Connection.emit (node:events:518:28)
      at Socket.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\connection.js:62:12)
      at Socket.emit (node:events:530:35)
      at TCP.<anonymous> (node:net:343:12),
  timestamp: '2025-08-04 11:37:06'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'production',
  timestamp: '2025-08-04 11:39:11'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:39:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:39:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -4077,
  code: 'ECONNRESET',
  syscall: 'read',
  level: 'error',
  message: 'Database connection failed: read ECONNRESET',
  stack: 'Error: read ECONNRESET\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async runMigrations (D:\\Task_Project\\backend\\src\\database\\migrate.js:206:5)\n' +
    '    at async main (D:\\Task_Project\\backend\\src\\database\\migrate.js:302:9)',
  timestamp: '2025-08-04 11:39:52'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:09'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:40:09'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:09'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:09'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:09'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:17'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:40:18'
}
{
  message: 'Starting database migrations...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:18'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT MAX(version) as version FROM migrations',
  duration: '260ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:18'
}
{
  message: 'Current migration version: 0',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:18'
}
{
  message: 'Found 3 pending migrations',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:18'
}
{
  message: 'Running migration 1: create_initial_tables',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:18'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:20'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '410ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:21'
}
{
  message: 'Migration 1 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:21'
}
{
  message: 'Running migration 2: add_indexes',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:21'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '291ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:23'
}
{
  message: 'Migration 2 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:23'
}
{
  message: 'Running migration 3: add_migration_tracking',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:23'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:25'
}
{
  message: 'Migration 3 completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:26'
}
{
  message: 'All migrations completed successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:26'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:40:26'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:42:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-04 11:42:48'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:42:48'
}
{
  message: 'Categories seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:42:51'
}
{
  message: 'Courses seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:42:53'
}
{
  message: 'Lessons seeded successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:43:00'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:43:00'
}
{
  message: 'Database pool closed',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-04 11:43:00'
}
