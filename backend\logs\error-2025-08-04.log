{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'error',
  message: 'Database connection failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async testConnection (D:\\Task_Project\\backend\\test-db.js:9:5)',
  timestamp: '2025-08-04 06:21:14'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'error',
  message: 'Database connection failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async testConnection (D:\\Task_Project\\backend\\test-db.js:23:5)',
  timestamp: '2025-08-04 06:22:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT MAX(version) as version FROM migrations',
  duration: '11ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '2ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '1ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:25:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:14'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:44'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'D:\\Task_Project\\backend\\src\\config\\firebase.js',
    'D:\\Task_Project\\backend\\src\\server.js'
  ],
  level: 'error',
  message: "Failed to initialize Firebase Admin SDK: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js',
  stack: "Error: Cannot find module 'D:\\Task_Project\\backend\\config\\firebase-admin-sdk.json'\n" +
    'Require stack:\n' +
    '- D:\\Task_Project\\backend\\src\\config\\firebase.js\n' +
    '- D:\\Task_Project\\backend\\src\\server.js\n' +
    '    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at initializeFirebase (D:\\Task_Project\\backend\\src\\config\\firebase.js:17:30)\n' +
    '    at Object.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:32:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1689:10)',
  timestamp: '2025-08-04 06:27:54'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '38ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '44ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a8af1690-0d26-43f0-962f-f8caed7f435f',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '44ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '1ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3218eb4c-c9af-4a5c-9448-e967a555ba50',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '4ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '35ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '37ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '20e102a2-7aba-4339-8715-695ffc7c5fd5',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '37ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:34:24'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '6ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '10ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'adf050f3-2370-4362-877f-4ab409be7a38',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '10ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '1ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '4ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '902c021d-d644-4920-8c24-cf4440e580a8',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '4ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '144ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '148ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4de9af76-5a11-4473-8398-bbf7ecd1d760',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '148ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:35:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '133ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:145:20'
  },
  context: {
    url: '/api/courses/categories',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.8.0',
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 500,
  responseTime: '137ms',
  userAgent: 'curl/8.8.0',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e106aab4-b61f-4566-b1c8-1a1ad2b81e68',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/categories',
  responseTime: '137ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:38:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '2ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '9ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'a7122903-72b5-4b9e-959d-690e85448e6d',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '9ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '144ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/popular',
    method: 'GET',
    ip: '::1',
    userAgent: undefined,
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular',
  statusCode: 500,
  responseTime: '148ms',
  userAgent: undefined,
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '75ed0011-7d27-4fda-b41d-064f824595fe',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/popular',
  responseTime: '148ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT \n' +
    '        c.id,\n' +
    '        c.title,\n' +
    '        c.description,\n' +
    '        c.image_url,\n' +
    '        c.instruc...',
  duration: '33ms',
  error: 'invalid input syntax for type integer: "NaN"',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  error: {
    name: 'error',
    message: 'invalid input syntax for type integer: "NaN"',
    stack: 'error: invalid input syntax for type integer: "NaN"\n' +
      '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async query (D:\\Task_Project\\backend\\src\\config\\database.js:83:20)\n' +
      '    at async D:\\Task_Project\\backend\\src\\routes\\course.js:179:20'
  },
  context: {
    url: '/api/courses/featured',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.8.0',
    userId: undefined,
    body: {},
    params: {},
    query: {}
  },
  level: 'error',
  message: 'Server Error',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured',
  statusCode: 500,
  responseTime: '35ms',
  userAgent: 'curl/8.8.0',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '73de982a-67c5-4fc3-a7dc-3ea1c4e39fa7',
  statusCode: 500,
  method: 'GET',
  url: '/api/courses/featured',
  responseTime: '35ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 06:40:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  responseTime: '8ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '766eeb4b-45b1-4ab6-8d07-1a53f1555341',
  statusCode: 404,
  method: 'GET',
  url: '/favicon.ico',
  responseTime: '8ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 08:49:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/courses',
  statusCode: 404,
  responseTime: '3ms',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ip: '::1',
  level: 'error',
  message: 'HTTP Request',
  timestamp: '2025-08-04 08:49:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3eb3811a-cea5-4797-b1c9-c9db3d657d54',
  statusCode: 404,
  method: 'GET',
  url: '/courses',
  responseTime: '3ms',
  userId: undefined,
  level: 'error',
  message: 'Error response',
  timestamp: '2025-08-04 08:49:45'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: '************************************************************************************************************',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL ************************************************************************************************************',
  stack: 'Error: getaddrinfo EAI_FAIL **************************************************************************************************************' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:49:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: 'postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  stack: 'Error: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: 'postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning',
  stack: 'Error: getaddrinfo EAI_FAIL postgresql://quipgen_elearning:<EMAIL>/quipgen_elearning\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:08'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3004,
  code: 'EAI_FAIL',
  syscall: 'getaddrinfo',
  hostname: '************************************************************************************************************',
  level: 'error',
  message: 'Database connection failed: getaddrinfo EAI_FAIL ************************************************************************************************************',
  stack: 'Error: getaddrinfo EAI_FAIL **************************************************************************************************************' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:50:41'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:51:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:10'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:52:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:58:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:59:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -4077,
  code: 'ECONNRESET',
  syscall: 'read',
  level: 'error',
  message: 'Database connection failed: read ECONNRESET',
  stack: 'Error: read ECONNRESET\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 10:59:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  length: 107,
  name: 'error',
  severity: 'FATAL',
  code: '3D000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postinit.c',
  line: '1032',
  routine: 'InitPostgres',
  level: 'error',
  message: 'Database connection failed: database "quipgen_elearning_app" does not exist',
  stack: 'error: database "quipgen_elearning_app" does not exist\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:01:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  length: 107,
  name: 'error',
  severity: 'FATAL',
  code: '3D000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postinit.c',
  line: '1032',
  routine: 'InitPostgres',
  level: 'error',
  message: 'Database connection failed: database "quipgen_elearning_app" does not exist',
  stack: 'error: database "quipgen_elearning_app" does not exist\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:01:47'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'error',
  message: 'Database connection failed: Connection terminated due to connection timeout',
  stack: 'Error: Connection terminated due to connection timeout\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  cause: Error: Connection terminated unexpectedly
      at Connection.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\client.js:136:73)
      at Object.onceWrapper (node:events:632:28)
      at Connection.emit (node:events:518:28)
      at Socket.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\connection.js:62:12)
      at Socket.emit (node:events:530:35)
      at TCP.<anonymous> (node:net:343:12),
  timestamp: '2025-08-04 11:34:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  errno: -3008,
  code: 'ENOTFOUND',
  syscall: 'getaddrinfo',
  hostname: 'dpg-d28447muk2gs73erfae0-a',
  level: 'error',
  message: 'Database connection failed: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a',
  stack: 'Error: getaddrinfo ENOTFOUND dpg-d28447muk2gs73erfae0-a\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:34:19'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'production',
  level: 'error',
  message: 'Database connection failed: Connection terminated due to connection timeout',
  stack: 'Error: Connection terminated due to connection timeout\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async runMigrations (D:\\Task_Project\\backend\\src\\database\\migrate.js:206:5)\n' +
    '    at async main (D:\\Task_Project\\backend\\src\\database\\migrate.js:302:9)',
  cause: Error: Connection terminated unexpectedly
      at Connection.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\client.js:136:73)
      at Object.onceWrapper (node:events:632:28)
      at Connection.emit (node:events:518:28)
      at Socket.<anonymous> (D:\Task_Project\backend\node_modules\pg\lib\connection.js:62:12)
      at Socket.emit (node:events:530:35)
      at TCP.<anonymous> (node:net:343:12),
  timestamp: '2025-08-04 11:37:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  length: 37,
  name: 'error',
  severity: 'FATAL',
  code: '28000',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: undefined,
  line: undefined,
  routine: undefined,
  level: 'error',
  message: 'Database connection failed: SSL/TLS required',
  stack: 'error: SSL/TLS required\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async Server.<anonymous> (D:\\Task_Project\\backend\\src\\server.js:133:5)',
  timestamp: '2025-08-04 11:39:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  errno: -4077,
  code: 'ECONNRESET',
  syscall: 'read',
  level: 'error',
  message: 'Database connection failed: read ECONNRESET',
  stack: 'Error: read ECONNRESET\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:47:20)\n' +
    '    at async runMigrations (D:\\Task_Project\\backend\\src\\database\\migrate.js:206:5)\n' +
    '    at async main (D:\\Task_Project\\backend\\src\\database\\migrate.js:302:9)',
  timestamp: '2025-08-04 11:39:52'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'SELECT MAX(version) as version FROM migrations',
  duration: '260ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:18'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '410ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:21'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  text: 'INSERT INTO migrations (version, name) VALUES ($1, $2)',
  duration: '291ms',
  error: 'relation "migrations" does not exist',
  level: 'error',
  message: 'Query error',
  timestamp: '2025-08-04 11:40:23'
}
